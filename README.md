# TradingAgents Frontend

<p align="center">
  <img src="https://img.shields.io/badge/Next.js-14.0-black?logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/React-18.0-blue?logo=react" alt="React" />
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?logo=typescript" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind-3.3-blue?logo=tailwindcss" alt="Tailwind CSS" />
</p>

TradingAgents 多智能体大语言模型金融交易框架的前端界面，基于 Next.js 构建的现代化 Web 应用程序。

## 🌟 特性

- **🎨 现代化界面**: 基于 Tailwind CSS 的响应式设计
- **🚀 高性能**: Next.js 14 + React 18 的最新技术栈
- **🔄 实时更新**: WebSocket 实时数据推送
- **📊 数据可视化**: 丰富的图表和数据展示
- **🌐 中文本地化**: 完整的中文界面支持
- **📱 移动端适配**: 完美的移动端体验
- **🎭 动画效果**: Framer Motion 流畅动画
- **🔧 TypeScript**: 完整的类型安全
- **🧠 LangGraph 集成**: 智能工作流和对话式分析
- **🤖 AI 驱动**: 基于大语言模型的智能分析

## 🏗️ 技术栈

### 核心框架
- **Next.js 14**: React 全栈框架
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript

### 样式和UI
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Headless UI**: 无样式的可访问组件
- **Heroicons**: 精美的 SVG 图标
- **Lucide React**: 现代图标库
- **Framer Motion**: 动画库

### 数据管理
- **TanStack Query**: 服务器状态管理
- **Zustand**: 客户端状态管理
- **Axios**: HTTP 客户端

### AI 和工作流
- **LangGraph.js**: 智能工作流引擎
- **LangChain.js**: 大语言模型集成
- **Zod**: 数据验证和类型安全

### 数据可视化
- **Recharts**: React 图表库

### 开发工具
- **ESLint**: 代码检查
- **PostCSS**: CSS 处理
- **Autoprefixer**: CSS 前缀自动添加

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd trading-agents-frontend
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **环境配置**
```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，配置必要的环境变量：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# OpenAI API配置
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_OPENAI_BASE_URL=https://api.nuwaapi.com

# FinnHub API配置
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key
```

4. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

5. **访问应用**

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
trading-agents-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React 组件
│   │   ├── dashboard/         # 仪表板组件
│   │   ├── langgraph/         # LangGraph 组件
│   │   ├── layout/           # 布局组件
│   │   ├── ui/               # 基础 UI 组件
│   │   ├── common/           # 通用组件
│   │   └── welcome/          # 欢迎页组件
│   ├── hooks/                # 自定义 Hooks
│   ├── lib/                  # 工具库
│   │   ├── api.ts           # API 接口
│   │   └── langgraph.ts     # LangGraph 配置
│   ├── store/                # 状态管理
│   ├── types/                # TypeScript 类型定义
│   └── utils/                # 工具函数
├── public/                  # 静态资源
├── .env.local.example      # 环境变量示例
├── next.config.js          # Next.js 配置
├── tailwind.config.js      # Tailwind CSS 配置
├── tsconfig.json          # TypeScript 配置
└── package.json           # 项目依赖
```

## 🎯 主要功能

### 1. 欢迎页面
- 项目介绍和功能展示
- 分析配置表单
- 工作流程说明

### 2. 交易仪表板
- **总览**: 分析进度和配置信息
- **代理状态**: 实时监控各代理工作状态
- **实时数据**: 股价、技术指标、新闻、基本面数据
- **分析报告**: 各代理生成的详细报告
- **交易决策**: 最终的交易建议和参数

### 3. 实时功能
- WebSocket 连接实时更新
- 自动数据刷新
- 状态同步

### 4. LangGraph 智能分析
- **对话式分析**: 通过自然语言进行股票分析
- **智能工作流**: 可视化的分析流程和状态管理
- **工具集成**: 自动调用合适的分析工具
- **内存管理**: 保持对话上下文和分析历史

## 🔌 API 集成

### 后端接口

应用与 TradingAgents 后端通过以下接口通信：

- `POST /api/analysis/start` - 开始分析
- `GET /api/analysis/{id}/status` - 获取分析状态
- `GET /api/analysis/{id}/agents` - 获取代理状态
- `GET /api/analysis/{id}/reports` - 获取分析报告
- `GET /api/analysis/{id}/decision` - 获取交易决策
- `WebSocket /ws/analysis/{id}` - 实时数据推送

### 数据接口

- `GET /api/data/stock/{ticker}` - 股票数据
- `GET /api/data/news/{ticker}` - 新闻数据
- `GET /api/data/technical/{ticker}` - 技术指标
- `GET /api/data/fundamentals/{ticker}` - 基本面数据

## 🎨 界面设计

### 设计原则
- **简洁明了**: 清晰的信息层次
- **响应式**: 适配各种屏幕尺寸
- **可访问性**: 符合 WCAG 标准
- **一致性**: 统一的设计语言

### 主题配置
- 支持深色/浅色主题
- 自定义颜色方案
- 响应式字体大小

## 🔧 开发指南

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 React/TypeScript 最佳实践
- 组件采用函数式编程

### 组件开发
```tsx
// 示例组件结构
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ComponentProps {
  // 定义 props 类型
}

export function Component({ }: ComponentProps) {
  // 组件逻辑
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {/* 组件内容 */}
    </motion.div>
  );
}
```

### 状态管理
```tsx
// 使用 TanStack Query 管理服务器状态
const { data, isLoading } = useQuery({
  queryKey: ['key'],
  queryFn: fetchData,
});

// 使用 Zustand 管理客户端状态
const useStore = create((set) => ({
  // 状态定义
}));
```

## 📦 构建和部署

### 构建生产版本
```bash
npm run build
npm run start
```

### 类型检查
```bash
npm run type-check
```

### 代码检查
```bash
npm run lint
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## ⚠️ 免责声明

本框架仅用于研究目的。交易表现可能因多种因素而异，包括所选的骨干语言模型、模型温度、交易周期、数据质量和其他非确定性因素。**不构成财务、投资或交易建议。**

## 🤖 AI 交互工具集

本项目集成了丰富的AI交互工具，支持智能化的股票分析和对话式交互：

### 核心AI工具

#### 1. **股票分析工具** (`stockAnalysisTool`)
- **功能**: 综合股票分析
- **支持类型**:
  - `fundamentals` - 基本面分析
  - `technical` - 技术面分析
  - `news` - 新闻分析
  - `sentiment` - 情绪分析
- **参数**: 股票代码(ticker)、分析类型(analysisType)

#### 2. **市场数据工具** (`marketDataTool`)
- **功能**: 获取实时市场数据
- **数据类型**:
  - `price` - 价格数据
  - `volume` - 成交量数据
  - `indicators` - 技术指标
- **时间周期**: 1天、1周、1月、3月、1年
- **参数**: 股票代码(ticker)、数据类型(dataType)、时间周期(period)

#### 3. **新闻分析工具** (`newsAnalysisTool`)
- **功能**: 分析股票相关新闻
- **特性**:
  - 新闻情绪分析
  - 影响评估
  - 舆情监控
- **参数**: 股票代码(ticker)、是否情绪分析(sentiment)

#### 4. **风险评估工具** (`riskAssessmentTool`)
- **功能**: 投资风险评估
- **评估维度**:
  - 单股风险评估
  - 投资组合风险分析
  - 风险等级评定
- **参数**: 股票代码(ticker)、投资组合(portfolio)、风险偏好(riskLevel)

### AI交互组件

#### 1. **LangGraph 智能代理** (`TradingAgent`)
- **核心功能**:
  - `analyze(ticker, config)` - 股票分析
  - `chat(message, threadId)` - 对话交互
  - `streamAnalysis(ticker, config)` - 流式分析
  - `getState(threadId)` - 获取状态

#### 2. **对话式分析界面** (`LangGraphChat`)
- **特性**:
  - 实时对话交互
  - 消息历史管理
  - 流式响应显示
  - 错误处理和重试

#### 3. **工作流可视化** (`WorkflowVisualization`)
- **功能**:
  - 实时工作流状态监控
  - 节点执行进度显示
  - 执行路径可视化
  - 性能指标统计

#### 4. **智能配置界面** (`LangGraphConfig`)
- **配置项**:
  - LLM模型选择和参数
  - 工作流执行策略
  - 内存管理设置
  - 调试和监控选项

### React Hooks

#### 1. **useLangGraphAgent**
- **状态管理**:
  - `messages` - 对话消息列表
  - `isProcessing` - 处理状态
  - `currentStep` - 当前执行步骤
  - `analysisResults` - 分析结果
  - `tradingDecision` - 交易决策
  - `error` - 错误信息

- **操作方法**:
  - `analyzeStock(ticker, config)` - 开始股票分析
  - `sendMessage(message)` - 发送聊天消息
  - `streamAnalysis(ticker, config)` - 流式分析
  - `getAgentState()` - 获取代理状态
  - `clearConversation()` - 清除对话
  - `retry()` - 重试操作

### 工作流节点

#### 1. **分析节点**
- `start` - 开始节点
- `data_collection` - 数据收集
- `fundamental_analysis` - 基本面分析
- `technical_analysis` - 技术分析
- `sentiment_analysis` - 情绪分析
- `risk_assessment` - 风险评估
- `decision_making` - 决策制定
- `end` - 结束节点

#### 2. **工具节点** (`ToolNode`)
- 自动工具调用
- 并行工具执行
- 工具结果聚合
- 错误处理和重试

### 使用示例

```typescript
// 1. 基础股票分析
const { analyzeStock } = useLangGraphAgent();
await analyzeStock('NVDA', {
  analysisType: 'comprehensive',
  includeRisk: true,
  includeSentiment: true,
});

// 2. 对话式交互
const { sendMessage } = useLangGraphAgent();
await sendMessage("请分析 AAPL 的投资价值");

// 3. 流式分析
const { streamAnalysis } = useLangGraphAgent();
for await (const chunk of streamAnalysis('TSLA')) {
  console.log('分析进度:', chunk);
}

// 4. 自定义工具
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

const customTool = tool(
  async ({ param1, param2 }) => {
    // 自定义工具逻辑
    return 'result';
  },
  {
    name: 'custom_tool',
    description: '自定义工具描述',
    schema: z.object({
      param1: z.string(),
      param2: z.number(),
    }),
  }
);
```

## 🔗 相关链接

- [TradingAgents 后端项目](https://github.com/TauricResearch/TradingAgents)
- [研究论文](https://arxiv.org/abs/2412.20138)
- [LangGraph 使用指南](./LANGGRAPH_GUIDE.md)
- [LangGraph.js 官方文档](https://langchain-ai.github.io/langgraphjs/)
- [Tauric Research](https://tauric.ai/)
- [Discord 社区](https://discord.com/invite/hk9PGKShPK)

## 📞 支持

如有问题或建议，请通过以下方式联系：

- GitHub Issues
- Discord 社区
- 邮箱: <EMAIL>
