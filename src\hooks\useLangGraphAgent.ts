'use client';

import { useState, useCallback, useRef } from 'react';
import { tradingAgent, TradingAgent } from '@/lib/langgraph';
import { HumanMessage } from '@langchain/core/messages';
import toast from 'react-hot-toast';

interface AgentMessage {
  id: string;
  type: 'human' | 'ai' | 'tool' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

interface AgentState {
  messages: AgentMessage[];
  isProcessing: boolean;
  currentStep: string;
  analysisResults: any;
  tradingDecision: any;
  error: string | null;
}

export function useLangGraphAgent() {
  const [state, setState] = useState<AgentState>({
    messages: [],
    isProcessing: false,
    currentStep: '',
    analysisResults: null,
    tradingDecision: null,
    error: null,
  });

  const agentRef = useRef<TradingAgent>(tradingAgent);
  const threadIdRef = useRef<string>('');

  // 生成新的线程ID
  const generateThreadId = useCallback(() => {
    const id = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    threadIdRef.current = id;
    return id;
  }, []);

  // 添加消息到状态
  const addMessage = useCallback((message: Omit<AgentMessage, 'id' | 'timestamp'>) => {
    const newMessage: AgentMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
    }));

    return newMessage;
  }, []);

  // 更新当前步骤
  const updateStep = useCallback((step: string) => {
    setState(prev => ({ ...prev, currentStep: step }));
  }, []);

  // 开始股票分析
  const analyzeStock = useCallback(async (ticker: string, config: any = {}) => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        currentStep: '初始化分析',
      }));

      // 生成新的线程ID
      const threadId = generateThreadId();

      // 添加用户消息
      addMessage({
        type: 'human',
        content: `请分析股票 ${ticker}`,
        metadata: { ticker, config },
      });

      updateStep('正在分析股票数据...');

      // 调用LangGraph代理
      const result = await agentRef.current.analyze(ticker, config);

      // 处理结果
      if (result.messages && result.messages.length > 0) {
        const lastMessage = result.messages[result.messages.length - 1];
        addMessage({
          type: 'ai',
          content: lastMessage.content || '分析完成',
          metadata: result,
        });
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: '分析完成',
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
      }));

      toast.success('股票分析完成');
      return result;

    } catch (error) {
      console.error('分析失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
        currentStep: '分析失败',
      }));

      addMessage({
        type: 'system',
        content: `分析失败: ${errorMessage}`,
      });

      toast.error(`分析失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 发送聊天消息
  const sendMessage = useCallback(async (message: string) => {
    try {
      setState(prev => ({ ...prev, isProcessing: true, error: null }));

      // 添加用户消息
      addMessage({
        type: 'human',
        content: message,
      });

      // 如果没有线程ID，生成一个
      if (!threadIdRef.current) {
        generateThreadId();
      }

      updateStep('处理消息...');

      // 调用代理
      const result = await agentRef.current.chat(message, threadIdRef.current);

      // 添加AI回复
      if (result.messages && result.messages.length > 0) {
        const lastMessage = result.messages[result.messages.length - 1];
        addMessage({
          type: 'ai',
          content: lastMessage.content || '已处理您的消息',
          metadata: result,
        });
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: '消息处理完成',
      }));

      return result;

    } catch (error) {
      console.error('消息发送失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
      }));

      addMessage({
        type: 'system',
        content: `消息发送失败: ${errorMessage}`,
      });

      toast.error(`消息发送失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 流式分析
  const streamAnalysis = useCallback(async function* (ticker: string, config: any = {}) {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        currentStep: '开始流式分析',
      }));

      // 生成新的线程ID
      generateThreadId();

      // 添加用户消息
      addMessage({
        type: 'human',
        content: `请流式分析股票 ${ticker}`,
        metadata: { ticker, config },
      });

      // 获取流式结果
      const stream = await agentRef.current.streamAnalysis(ticker, config);

      for await (const chunk of stream) {
        // 更新步骤
        if (chunk.currentStep) {
          updateStep(chunk.currentStep);
        }

        // 添加消息
        if (chunk.messages && chunk.messages.length > 0) {
          const lastMessage = chunk.messages[chunk.messages.length - 1];
          addMessage({
            type: 'ai',
            content: lastMessage.content || '处理中...',
            metadata: chunk,
          });
        }

        // 更新状态
        setState(prev => ({
          ...prev,
          analysisResults: chunk.analysisResults || prev.analysisResults,
          tradingDecision: chunk.tradingDecision || prev.tradingDecision,
        }));

        yield chunk;
      }

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: '流式分析完成',
      }));

    } catch (error) {
      console.error('流式分析失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
        currentStep: '流式分析失败',
      }));

      toast.error(`流式分析失败: ${errorMessage}`);
      throw error;
    }
  }, [addMessage, updateStep, generateThreadId]);

  // 获取代理状态
  const getAgentState = useCallback(async () => {
    if (!threadIdRef.current) return null;

    try {
      const agentState = await agentRef.current.getState(threadIdRef.current);
      return agentState;
    } catch (error) {
      console.error('获取代理状态失败:', error);
      return null;
    }
  }, []);

  // 清除对话
  const clearConversation = useCallback(() => {
    setState({
      messages: [],
      isProcessing: false,
      currentStep: '',
      analysisResults: null,
      tradingDecision: null,
      error: null,
    });
    threadIdRef.current = '';
  }, []);

  // 重试最后一个操作
  const retry = useCallback(async () => {
    const lastHumanMessage = state.messages
      .filter(msg => msg.type === 'human')
      .pop();

    if (!lastHumanMessage) {
      toast.error('没有可重试的操作');
      return;
    }

    // 检查是否是分析请求
    if (lastHumanMessage.metadata?.ticker) {
      return analyzeStock(
        lastHumanMessage.metadata.ticker,
        lastHumanMessage.metadata.config
      );
    } else {
      return sendMessage(lastHumanMessage.content);
    }
  }, [state.messages, analyzeStock, sendMessage]);

  return {
    // 状态
    messages: state.messages,
    isProcessing: state.isProcessing,
    currentStep: state.currentStep,
    analysisResults: state.analysisResults,
    tradingDecision: state.tradingDecision,
    error: state.error,
    threadId: threadIdRef.current,

    // 操作
    analyzeStock,
    sendMessage,
    streamAnalysis,
    getAgentState,
    clearConversation,
    retry,
  };
}
